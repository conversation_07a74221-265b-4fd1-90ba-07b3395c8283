export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => boolean;
  message: string;
}

export interface ValidationRules {
  [key: string]: ValidationRule[];
}

export interface ValidationErrors {
  [key: string]: string;
}

export function validateField(
  value: string,
  rules: ValidationRule[]
): string | undefined {
  for (const rule of rules) {
    if (rule.required && (!value || value.trim() === '')) {
      return rule.message;
    }

    if (value && rule.minLength && value.length < rule.minLength) {
      return rule.message;
    }

    if (value && rule.maxLength && value.length > rule.maxLength) {
      return rule.message;
    }

    if (value && rule.pattern && !rule.pattern.test(value)) {
      return rule.message;
    }

    if (value && rule.custom && !rule.custom(value)) {
      return rule.message;
    }
  }

  return undefined;
}

export function validateForm(
  data: Record<string, string>,
  rules: ValidationRules
): ValidationErrors {
  const errors: ValidationErrors = {};

  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field] || '';
    const error = validateField(value, fieldRules);
    if (error) {
      errors[field] = error;
    }
  }

  return errors;
}

// Common validation rules
export const commonRules = {
  email: [
    { required: true, message: 'Email is required' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Please enter a valid email address',
    },
  ],
  password: [
    { required: true, message: 'Password is required' },
    { minLength: 6, message: 'Password must be at least 6 characters long' },
  ],
  confirmPassword: (password: string) => [
    { required: true, message: 'Please confirm your password' },
    {
      custom: (value: string) => value === password,
      message: 'Passwords do not match',
    },
  ],
  required: (fieldName: string) => [
    { required: true, message: `${fieldName} is required` },
  ],
};
