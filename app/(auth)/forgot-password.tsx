import React, { useState } from 'react';
import { Link, router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { useAuthForm } from '@/hooks/useAuthForm';

export default function ForgotPassword() {
  const { formState, errors, loading, updateField, handleSubmit } =
    useAuthForm('forgot');
  const [emailSent, setEmailSent] = useState(false);

  const onSubmit = async () => {
    await handleSubmit();
    if (!errors.general) {
      setEmailSent(true);
    }
  };

  if (emailSent) {
    return (
      <Box className='flex-1 bg-background-0 justify-center px-6'>
        <VStack space='xl' className='w-full max-w-md mx-auto items-center'>
          <VStack space='md' className='items-center'>
            <Heading size='2xl' className='text-typography-900 text-center'>
              Check Your Email
            </Heading>
            <Text size='md' className='text-typography-500 text-center'>
              We have sent a password reset link to {formState.email}
            </Text>
          </VStack>

          <VStack space='lg' className='w-full'>
            <Button
              onPress={() => router.push('/(auth)/sign-in')}
              className='w-full'
            >
              <ButtonText>Back to Sign In</ButtonText>
            </Button>

            <Button
              variant='outline'
              onPress={() => setEmailSent(false)}
              className='w-full'
            >
              <ButtonText>Try Different Email</ButtonText>
            </Button>
          </VStack>
        </VStack>
      </Box>
    );
  }

  return (
    <Box className='flex-1 bg-background-0 justify-center px-6'>
      <VStack space='xl' className='w-full max-w-md mx-auto'>
        <VStack space='md' className='items-center'>
          <Heading size='2xl' className='text-typography-900'>
            Reset Password
          </Heading>
          <Text size='md' className='text-typography-500 text-center'>
            Enter your email address and we will send you a link to reset your
            password
          </Text>
        </VStack>

        <VStack space='lg'>
          <FormControl isInvalid={!!errors.email}>
            <FormControlLabel>
              <FormControlLabelText>Email</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type='text'
                placeholder='Enter your email'
                value={formState.email}
                onChangeText={text => updateField('email', text)}
                keyboardType='email-address'
                autoCapitalize='none'
                autoComplete='email'
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.email}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          {errors.general && (
            <Text size='sm' className='text-error-500 text-center'>
              {errors.general}
            </Text>
          )}

          <Button onPress={onSubmit} isDisabled={loading} className='w-full'>
            {loading && <ButtonSpinner />}
            <ButtonText>
              {loading ? 'Sending...' : 'Send Reset Link'}
            </ButtonText>
          </Button>
        </VStack>

        <HStack className='justify-center items-center' space='xs'>
          <Text size='sm' className='text-typography-500'>
            Remember your password?
          </Text>
          <Link href='/(auth)/sign-in' asChild>
            <Text size='sm' className='text-primary-600 font-medium'>
              Sign In
            </Text>
          </Link>
        </HStack>
      </VStack>
    </Box>
  );
}
