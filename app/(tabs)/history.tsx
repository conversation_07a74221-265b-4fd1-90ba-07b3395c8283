import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, ActivityIndicator } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@/contexts/AuthContext';
import {
  TransactionsService,
  CustomerTransaction,
  CustomerBusinessSummary,
} from '@/services';
import dayjs from 'dayjs';
import { GradientBar } from '@/components/auth/GradientBar';
import { RefreshControl } from '@/components/ui/refresh-control';

export default function History() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'transactions' | 'businesses'>(
    'transactions'
  );
  const [transactionData, setTransactionData] = useState<CustomerTransaction[]>(
    []
  );
  const [businessData, setBusinessData] = useState<CustomerBusinessSummary[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = useCallback(
    async (isRefresh = false) => {
      if (!user?.id) {
        if (!isRefresh) setLoading(false);
        return;
      }

      if (!isRefresh) setLoading(true);
      setError(null);

      try {
        // Fetch both transactions and business summaries in parallel
        const [transactionsResult, businessesResult] = await Promise.all([
          TransactionsService.getCustomerTransactionHistory(user.id),
          TransactionsService.getCustomerBusinessSummaries(user.id),
        ]);

        if (transactionsResult.error) {
          setError(transactionsResult.error);
        } else {
          setTransactionData(transactionsResult.data || []);
        }

        if (businessesResult.error && !transactionsResult.error) {
          setError(businessesResult.error);
        } else {
          setBusinessData(businessesResult.data || []);
        }
      } catch (err) {
        setError('An unexpected error occurred while fetching data');
        console.error('Error fetching history data:', err);
      }

      if (!isRefresh) setLoading(false);
    },
    [user?.id]
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchData(true),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            History
          </Heading>

          {/* Colored divider line */}
          <GradientBar />

          {/* Tab Toggle */}
          <HStack className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden'>
            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'transactions' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => setActiveTab('transactions')}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'transactions'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Transactions
              </Text>
            </Pressable>

            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'businesses' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => setActiveTab('businesses')}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'businesses'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Businesses
              </Text>
            </Pressable>
          </HStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            <Box className='flex-1 items-center justify-center py-12'>
              <ActivityIndicator size='large' color='#3B82F6' />
              <Text size='md' className='text-typography-600 mt-4'>
                Loading history...
              </Text>
            </Box>
          ) : error ? (
            <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
              <HStack space='md' className='items-center'>
                <FontAwesome
                  name='exclamation-triangle'
                  size={20}
                  color='#EF4444'
                />
                <VStack className='flex-1'>
                  <Text size='md' className='text-error-700 font-semibold'>
                    Error loading data
                  </Text>
                  <Text size='sm' className='text-error-600'>
                    {error}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          ) : activeTab === 'transactions' ? (
            <VStack space='md'>
              {transactionData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='history' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No transactions found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Your transaction history will appear here once you start
                    earning points
                  </Text>
                </Box>
              ) : (
                transactionData.map(item => (
                  <Box
                    key={item.id}
                    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
                  >
                    <HStack space='md' className='items-center'>
                      {/* Icon */}
                      <Box
                        className={`w-12 h-12 rounded-xl items-center justify-center ${
                          item.type === 'purchase'
                            ? 'bg-primary-500'
                            : 'bg-error-500'
                        }`}
                      >
                        <FontAwesome
                          name={
                            item.type === 'purchase' ? 'shopping-bag' : 'gift'
                          }
                          size={20}
                          color='white'
                        />
                      </Box>

                      {/* Content */}
                      <VStack className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {item.businessName}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {item.type === 'purchase' ? 'Purchase' : 'Redemption'}{' '}
                          • {item.businessCategory}
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          {dayjs(item.date).format('DD MMM YYYY, HH:mm')}
                        </Text>
                      </VStack>

                      {/* Points */}
                      <Text
                        size='lg'
                        className={`font-bold ${
                          item.type === 'redemption'
                            ? 'text-error-500'
                            : 'text-primary-500'
                        }`}
                      >
                        {item.type === 'redemption'
                          ? `-${item.pointsRedeemed ?? 0} pts`
                          : `${item.pointsEarned > 0 ? '+' : ''}${item.pointsEarned} pts`}
                      </Text>
                    </HStack>
                  </Box>
                ))
              )}
            </VStack>
          ) : (
            <VStack space='md'>
              {businessData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='building' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No businesses found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Businesses you visit will appear here
                  </Text>
                </Box>
              ) : (
                businessData.map(business => (
                  <Box
                    key={business.id}
                    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
                  >
                    <HStack space='md' className='items-center'>
                      {/* Icon */}
                      <Box className='w-12 h-12 bg-primary-500 rounded-xl items-center justify-center'>
                        <FontAwesome name='building' size={20} color='white' />
                      </Box>

                      {/* Content */}
                      <VStack className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {business.name}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {business.category}
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          Last visit:{' '}
                          {dayjs(business.lastVisit).format(
                            'DD MMM YYYY, HH:mm'
                          )}
                        </Text>
                      </VStack>

                      {/* Points */}
                      <Text size='lg' className='text-primary-500 font-bold'>
                        {business.points} pts
                      </Text>
                    </HStack>
                  </Box>
                ))
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
