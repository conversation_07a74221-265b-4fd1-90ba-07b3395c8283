import React, { useEffect, useState, useCallback } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';
import { useAuth } from '@/contexts/AuthContext';
import { PointsService, CustomerPointsSummary } from '@/services';
import { GradientBar } from '@/components/auth/GradientBar';
import { RefreshControl } from '@/components/ui/refresh-control';

export default function Home() {
  const { user } = useAuth();
  const [pointsData, setPointsData] = useState<CustomerPointsSummary | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch points data when component mounts or user changes
  const fetchPointsData = useCallback(async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const result = await PointsService.getCustomerPointsSummary(user.id);

    if (result.error) {
      setError(result.error);
      setPointsData(null);
    } else {
      setPointsData(result.data);
      setError(null);
    }

    setLoading(false);
  }, [user?.id]);

  useEffect(() => {
    fetchPointsData();
  }, [fetchPointsData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchPointsData(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Home
          </Heading>

          {/* Colored divider line */}
          <GradientBar />

          {/* Greeting */}
          <VStack space='xs' className='items-center'>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              Good afternoon.
            </Heading>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              Welcome to Indie Points!
            </Heading>
            <Text size='md' className='text-typography-600 text-center'>
              Here is your points summary.
            </Text>
          </VStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading your points...
              </Text>
            </VStack>
          ) : error ? (
            // Error state
            <VStack space='lg' className='items-center py-8'>
              <Text
                size='lg'
                className='text-error-500 font-semibold text-center'
              >
                Unable to load points data
              </Text>
              <Text size='md' className='text-typography-600 text-center'>
                {error}
              </Text>
              <Text size='sm' className='text-typography-500 text-center'>
                Please try again later or contact support if the problem
                persists.
              </Text>
            </VStack>
          ) : (
            // Data loaded successfully
            <VStack space='lg'>
              {/* Active Points Card */}
              <Box className='bg-primary-500 rounded-2xl border-4 border-primary-700 shadow-lg p-6'>
                <VStack space='xs'>
                  <Text size='lg' className='text-white font-medium'>
                    Active points
                  </Text>
                  <Heading size='4xl' className='text-white font-bold'>
                    {pointsData?.totalActive?.toLocaleString() || '0'}
                  </Heading>
                </VStack>
              </Box>

              {/* Total Earned and Redeemed Cards */}
              <HStack space='md'>
                {/* Total Earned Card */}
                <Box className='flex-1 bg-secondary-500 rounded-2xl border-4 border-secondary-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Total earned
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.totalEarned?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>

                {/* Total Redeemed Card */}
                <Box className='flex-1 bg-error-500 rounded-2xl border-4 border-error-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Total redeemed
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.totalRedeemed?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>
              </HStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
