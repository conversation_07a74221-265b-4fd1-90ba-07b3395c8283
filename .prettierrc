{"printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.json", "options": {"printWidth": 120, "tabWidth": 2}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.html", "options": {"printWidth": 120, "htmlWhitespaceSensitivity": "ignore"}}, {"files": ["*.css", "*.scss", "*.less"], "options": {"printWidth": 100, "singleQuote": false}}]}